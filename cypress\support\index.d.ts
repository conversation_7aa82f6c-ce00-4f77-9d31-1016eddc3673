/// <reference types="cypress" />

// Global Cypress declarations
declare global {
  const cy: Cypress.cy & {
    intercept: Cypress.cy['intercept'];
  };
  const Cypress: Cypress.Cypress;
  const expect: Chai.ExpectStatic;

  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to select DOM element by data-cy attribute.
       * @example cy.dataCy('greeting')
       */
      dataCy(value: string): Chainable<JQuery<HTMLElement>>;

      /**
       * Custom command to get element by data-test-id
       */
      getDataIdCy(options: { idAlias: string }): Chainable<JQuery<HTMLElement>>;

      /**
       * Custom command for GraphQL requests
       */
      Graphql<T = any>(query: string, variables?: any): Chainable<{ body: T }>;

      /**
       * Custom command to go to test file
       */
      GoToTestFile(name: string): Chainable<void>;

      /**
       * Custom command to delete TDO by name
       */
      deleteTdoByName(nameList: string[]): Chainable<void>;

      /**
       * Custom command to delete profile by name
       */
      deleteProfileByName(name: string): Chainable<void>;

      /**
       * Custom command to clear trim range
       */
      clearTrimRange(tdoId: string): Chainable<void>;

      /**
       * Custom command to delete all redacted
       */
      deleteAllRedacted(tdoId: string): Chainable<void>;
    }
  }
}

export {};
