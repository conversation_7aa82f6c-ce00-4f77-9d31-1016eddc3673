import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  Colors,
  dataIdPopupMap,
  DataTestSelector,
  Graphql,
  ordinalToNumber,
  UdrMenuList,
  UdrShape,
  VideoResult,
  waitForJobSuccess,
} from '../../../support/helperFunction/mediaDetailHelper';

Before({ tags: '@small-screen' }, () => {
  cy.viewport(1280, 720);
});

Given('The user is on the video overlay', () => {
  cy.get('.video-react-video').should('be.exist');
});

Given('There should be no existing UDRs', () => {
  cy.getDataIdCy({
    idAlias: `${DataTestSelector.OverlayContainer} > .react-draggable`,
  }).should('not.exist');
});

Given('The user resets and goes to video {string}', (videoName: string) => {
  cy.ResetAndGoToTestFile(videoName);

  cy.get('.video-react-video').should('be.exist');
  cy.getDataIdCy({
    idAlias: `${DataTestSelector.OverlayContainer} > .react-draggable`,
  }).should('not.exist');
});

Then(
  'The user draws {string} UDR at coordinates {int}, {int}, {int}, {int}',
  (udrNumber: string, x1: number, y1: number, x2: number, y2: number) => {
    cy.DrawAnUdr({
      orderNumber: (ordinalToNumber(udrNumber) || 1) - 1,
      x1,
      y1,
      x2,
      y2,
    });
  }
);

Then(
  'The user draws {int} UDRs with increasing coordinates',
  (count: number) => {
    cy.DrawCustomUDRsWithIncreasingCoordinates(count);
  }
);

Then('The user should see the UDR in the timeline and cluster list', () => {
  cy.getDataIdCy({ idAlias: `${DataTestSelector.FileDetailTimeline} > div` })
    .contains(/UDR.*▼/)
    .then(($parent) => {
      cy.wrap($parent).find('div').contains(/▼/).click();
      return;
    });
  cy.getDataIdCy({ idAlias: `${DataTestSelector.FileDetailTimeline}` })
    .contains(`${VideoResult.OverlayGroup} 1`)
    .should('have.length', 1);
  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .first()
    .within(() => {
      cy.contains(1).should('be.visible');
      // cy.get('span').should('contain', TimeRange.TimeRange0);
    });
});

When(
  'Store the number of {string} detection to {string}',
  (type: string, key: string) => {
    cy.get('[data-test="detection-name"]')
      .filter(
        (_index, element) =>
          Cypress.$(element).text().trim() === type.toUpperCase()
      )
      .then(($e) => {
        const totalElements = $e.length;
        return cy.setKeyValue(key, totalElements);
      });
  }
);

Then('The pop-up {string} should be shown', (popupName: string) => {
  const dataId = dataIdPopupMap[popupName as keyof typeof dataIdPopupMap];
  cy.getDataIdCy({ idAlias: dataId }).should('contain.text', popupName);
});

Then(
  'The user should see {string} in {string} row of UDR group name at the right panel is visible',
  (udrName: string, order: string) => {
    cy.get('[data-testid="virtuoso-scroller"]').scrollTo('bottom', {
      ensureScrollable: false,
    });
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .eq((ordinalToNumber(order) || 1) - 1)
      .within(() => {
        cy.contains(udrName).should('be.visible');
      });
  }
);

When(
  'The user opens UDR menu for UDR {int} with type {string}',
  (udrOrder: number, type: string) => {
    cy.get('[data-test="overlay-container"]').click({ force: true });
    cy.get('[data-test="overlay-container"]').then(() => {
      cy.get(
        `.react-draggable[data-testid="rnd-box"][type="${type}"]:eq(${udrOrder - 1})`
      ).rightclick({ force: true });
      return;
    });
  }
);

When('The user clicks on {string} in UDR menu', (udrMenuValue: UdrMenuList) => {
  cy.get('ul').find(`li:contains(${udrMenuValue})`).click();
});

When(
  'The user clicks on overlay group {int} in timeline',
  (udrOrder: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FileDetailTimeline })
      .contains(`Overlay Group ${udrOrder}`)
      .click();
  }
);

When(
  'The user presses the {string} button in {string} popup',
  (buttonName: string, popupName: string) => {
    const dataId = dataIdPopupMap[popupName as keyof typeof dataIdPopupMap];
    cy.getDataIdCy({ idAlias: dataId }).within(() => {
      cy.get(`button[type="button"]:contains(${buttonName})`).click();
    });
  }
);

When('Presses the {string} button', (btn: string) => {
  cy.get('button').contains(btn).click();
});

When('The user reloads the page', () => {
  cy.reload();
});

When('The user navigates to {string} Tab', (tabName: string) => {
  cy.getDataIdCy({ idAlias: `@${tabName}-tab-button` }).click();
});

When(
  'The user uploads file {string} with transcription {string}',
  (fileName: string, transcriptionState: string) => {
    if (transcriptionState === 'off') {
      cy.UploadFileWithoutEngine({ videoName: fileName }); // TO DO: Combined into 1 feature file to reduce file upload numbers
    } else {
      cy.UploadFileWithEngine({
        videoName: fileName,
        transcriptionState: transcriptionState,
      });
    }
    cy.intercept('POST', Graphql.GraphqlURL, (req) => {
      if (req.body.query === Graphql.JobQuery) {
        req.alias = 'jobStatus';
      }
    });

    cy.get('@tdoIdValue').then((targetId) => {
      waitForJobSuccess({
        maxRetryAttempts: 50,
        retryCount: 0,
        _targetId: targetId,
      });
      return;
    });
  }
);

When('The user goes to test file {string}', (fileName: string) => {
  cy.GoToTestFile(fileName);
  cy.get('[data-test="overlay-container"]', {
    timeout: 120000,
  }).should('be.visible');
  cy.get('body').should('not.contain', 'Fetching object detection');
});

When('The user navigates to Editor tab', () => {
  cy.getDataIdCy({ idAlias: 'results-tab-button' }).click();
  cy.getDataIdCy({ idAlias: 'results-tab-button' }).should(
    'have.attr',
    'aria-selected',
    'true'
  );
});

When('The user scrolls the navigation bar in right panel', () => {
  cy.get('[data-testid="virtuoso-scroller"]').scrollTo('bottom');
});

When(
  'The user selects checkbox for UDR {int} in the right panel',
  (udrOrder: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .eq(udrOrder - 1)
      .within(() => {
        cy.get('[data-testid="check-box-container"] > *').click();
      });
  }
);

Then(
  'The UDR {int} contains Redaction Code {string} and text color {string}',
  (udrOrder: number, redactionCode: string, textColor: Colors) => {
    cy.get('[data-testid^="redaction-code"]')
      .eq(udrOrder - 1)
      .contains(redactionCode)
      .toHaveCssProperty('color', textColor);
  }
);

Then(
  'The UDR {int} should not contain Redaction Code {string}',
  (udrOrder: number, redactionCode: string) => {
    cy.get('[data-test="overlay-container"]').then(() =>
      cy
        .get(`.react-draggable[data-testid="rnd-box"]:eq(${udrOrder - 1})`)
        .should('not.contain.text', redactionCode)
    );
  }
);

When('The user plays the video to {int} seconds', (seconds: number) => {
  cy.StopVideoAtSeconds(seconds);
});

When('The user clicks on Shape menu on UDR {int}', (udrOrder: number) => {
  cy.get(`.react-draggable[data-testid="rnd-box"]:eq(${udrOrder - 1})`).trigger(
    'mouseover',
    { force: true }
  );

  cy.getDataIdCy({ idAlias: 'Shapes-Menu' })
    .eq(udrOrder - 1)
    .click({ force: true });
});

Then(
  'The shape of the UDR {int} is {string}',
  (udrOrder: number, udrShape: UdrShape) => {
    cy.get(`.react-draggable[data-testid="rnd-box"]:eq(${udrOrder - 1})`)
      .invoke('attr', 'shape')
      .should('equal', udrShape);
  }
);

Then('The user is on {string} File Detail Screen', (fileName: string) => {
  cy.GoToTestFile(fileName);
  cy.get('[data-test="overlay-container"]', {
    timeout: 120000,
  }).should('be.visible');
});

Given('The user deletes files', (fileNames: DataTable) => {
  cy.deleteTdoByName(fileNames.raw().flat());
});
