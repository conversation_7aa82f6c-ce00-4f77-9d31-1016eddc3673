import { Before, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import {
  Colors,
  dataIdPopupMap,
  DataTestSelector,
  Graphql,
  ordinalToNumber,
  RedactionEffect,
  TimeRange,
  VideoResult,
} from '../../../support/helperFunction/mediaDetailHelper';

import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
  cy.intercept('POST', Graphql.GraphqlURL, (req) => {
    if (req.body.query === Graphql.AddBoundingBox) {
      req.alias = 'addBoundingBox';
    }
  });
});

Then(
  'The user should see the start time and end time of UDR in the right panel',
  () => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .first()
      .within(() => {
        cy.get('span').should('contain', TimeRange.TimeRange0);
      });
  }
);

When(
  'The user clicks on the overlay group {int} and plays the video to {int} seconds',
  (order: number, seconds: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FileDetailTimeline })
      .contains(`Overlay Group ${order}`)
      .click();
    cy.StopVideoAtSeconds(seconds);
  }
);

Then(
  'The user should see the updated UDR {string} in the {string} timeline and cluster list',
  (timeRange: string, order: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FileDetailTimeline })
      .contains(`Overlay Group ${ordinalToNumber(order)}`)
      .should('have.length', 1);
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .first()
      .within(() => {
        cy.getDataIdCy({ idAlias: DataTestSelector.TimePeriodSpan }).should(
          'have.text',
          timeRange
        );
      });
  }
);

When('The user deletes the UDR', () => {
  cy.DeleteUDRGroups();
  cy.contains(`[title=${VideoResult.OverlayGroup} 1`).should('not.exist');
});

When(
  'The user clicks on {string} radio in Overlay Preview',
  (previewName: string) => {
    cy.get(
      `[aria-label="Overlay Preview"] label:contains("${previewName}")`
    ).click();
  }
);

When(
  'The user selects {string} redaction effect',
  (redactionEffect: RedactionEffect) => {
    cy.getByRoles('combobox').filter(`:contains("Blur")`).click();
    cy.get(`[data-value="${redactionEffect}"]`).click();
  }
);

Then(
  'The timeline display a yellow line runs throughout the duration of the video',
  () => {
    cy.getDataIdCy({
      idAlias: DataTestSelector.FileDetailTimeline,
    }).within(() => {
      cy.get('[data-test="timeline-udr-title"]').toHaveCssProperty(
        'border-color',
        Colors.Gold
      );
    });
  }
);

Then(
  'The user sees {string} for UDR {int} should have {string} color',
  (cssElement: string, udrOrder: number, colorValue: Colors) => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
    ).within(() => {
      cy.get('[data-testid^="rnd-box-"]').should(
        'have.css',
        cssElement,
        colorValue
      );
    });
  }
);

Then('UDR {int} should have blur attribute', (udrOrder: number) => {
  cy.get(
    `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
  ).within(() => {
    cy.get('[data-testid^="rnd-box-"]').should(($el) => {
      const filterValue = $el.css('backdrop-filter');
      expect(filterValue).to.match(/blur\(\d+px\)/);
    });
  });
});

When('The user changes blur level to {int}', (blurLevel: number) => {
  cy.get('input[type="number"]').clear();
  cy.get('input[type="number"]').type(blurLevel.toString());
});

When('The user changes the UDR {int} shape', (udrOrder: number) => {
  cy.get(
    `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
  ).trigger('mouseover');

  cy.getDataIdCy({ idAlias: 'Shapes-Menu' })
    .eq(udrOrder - 1)
    .click({ force: true });
  // Temporary fix
  cy.getDataIdCy({ idAlias: 'Shapes-Menu' })
    .eq(udrOrder - 1)
    .click({ force: true });
});

When(
  'The user searches and selects Redaction Code {string}',
  (redactionCode: string) => {
    cy.get('input[id="autocomplete-RedactionCode"]').clear();
    cy.get('input[id="autocomplete-RedactionCode"]').type(redactionCode);

    cy.get('#autocomplete-RedactionCode-listbox')
      .contains('li', redactionCode)
      .click();
  }
);

When(
  'The user change redact color to {string} in {string} popup',
  (colorValue: Colors, popupName: string) => {
    const dataId = dataIdPopupMap[popupName as keyof typeof dataIdPopupMap];
    cy.getDataIdCy({ idAlias: dataId }).within(() => {
      cy.get('input[placeholder="#FFFFFF"][type="text"]').clear();
      cy.get('input[placeholder="#FFFFFF"][type="text"]').type(colorValue);
    });
  }
);

When(
  'The user set {string} time {string} for UDR',
  (timeStamp: string, time: string) => {
    cy.get(`[data-testid="time-stamp-range-editor-popup-${timeStamp}-seconds"]`)
      .as('timeStamp')
      .clear();
    cy.get('@timeStamp').type(`'${time}'`);
  }
);

When('The user click button Set to set time for UDR', () => {
  cy.get('[data-testid="time-stamp-range-editor-button-submit"]').click();
});

Then('The UDR {int} is not black-fill', (udrOrder: number) => {
  cy.get(
    `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
  ).within(() => {
    cy.get('[data-testid^="rnd-box-"]').toNotHaveCssProperty(
      'css',
      Colors.LightBlack
    );
  });
});

Then(
  'The user should see UDR set start time is {string} and end time is {string}',
  (startTime: string, endTime: string) => {
    cy.get(
      '[data-testid="time-stamp-range-editor-popup-Start-seconds"]'
    ).should('have.value', `${startTime}`);
    cy.get('[data-testid="time-stamp-range-editor-popup-End-seconds"]').should(
      'have.value',
      `${endTime}`
    );
  }
);

Then(
  'Screen display a red warning on {string} pop-up set time',
  (warningText: string) => {
    cy.contains(warningText).toHaveCssProperty('color', Colors.PastelRed);
  }
);

Then('The UDR {int} is deleted', (udrOrder: number) => {
  cy.get('[data-test="overlay-container"]').then(() => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
    ).should('not.exist');
    return;
  });
});

Then('The UDR group is deleted from the timeline and right panel', () => {
  cy.getDataIdCy({ idAlias: `${DataTestSelector.FileDetailTimeline}` })
    .contains(`${VideoResult.OverlayGroup} 1`)
    .should('not.exist');

  cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabVideo }).should(
    'not.contain.text',
    `${VideoResult.OverlayGroup} 1`
  );
});

When(
  'The user selects UDR {int} from the right panel',
  (udrRowNumber: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .contains(`${udrRowNumber}`)
      .click();
  }
);

Then('The UDR {int} is displayed in preview video', (udrOrder: number) => {
  cy.get('[data-test="overlay-container"]').then(() => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
    ).should('exist');
    return;
  });
});

Then('The UDR {int} does not display in preview video', (udrOrder: number) => {
  cy.get('[data-test="overlay-container"]').then(() => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${udrOrder - 1})`
    ).should('not.exist');
    return;
  });
});

When('The user unchecks all checkbox in the right panel', () => {
  cy.get('[data-testid="cluster-list-sort-container"]').within(() => {
    cy.get('[data-testid="checkbox-svg"]').click();
  });
});

When(
  'The user clicks on {string} in UDR group name at the right panel',
  (udrName: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .first()
      .within(() => {
        cy.contains(udrName).click();
      });
  }
);

When(
  'The user clicks on Pencil icon in UDR group name at the right panel',
  () => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
      .first()
      .within(() => {
        cy.get('[data-testid="click-to-edit-icon-edit"]').click({
          force: true,
        });
      });
  }
);

When('The user types {string} in name input', (name: string) => {
  cy.get('input[type="text"]').clear();
  cy.get('input[type="text"]').type(`${name}`);
  cy.get('[data-testid="click-to-edit-icon-check"]').click();
});

When('The user clicks on Redaction Effects menu in right panel', () => {
  cy.get('[data-testid="bulk-redaction-button"]').click();
});

When(
  'The user selects {string} redaction effect in right panel',
  (redactionEffect: string) => {
    cy.get('[data-testid="bulk-redaction-redaction-type"]').click();
    cy.get(`[data-value="${redactionEffect}"]`).click();
  }
);

Then('The user save new redaction effect in right panel', () => {
  cy.get('[data-testid="bulk-redaction-save-button"]').click();
});

When(
  'The user selects sort by {string} time in right panel',
  (timeStamp: string) => {
    cy.get(`[data-testid="box-sort-${timeStamp}-time-ms"]`).click();
  }
);
